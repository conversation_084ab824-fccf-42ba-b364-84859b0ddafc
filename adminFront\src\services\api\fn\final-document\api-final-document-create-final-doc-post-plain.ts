/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CreateFinalDocArgs } from '../../models/create-final-doc-args';
import { StringResponseBase } from '../../models/string-response-base';

export interface ApiFinalDocumentCreateFinalDocPost$Plain$Params {
      body?: CreateFinalDocArgs
}

export function apiFinalDocumentCreateFinalDocPost$Plain(http: HttpClient, rootUrl: string, params?: ApiFinalDocumentCreateFinalDocPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiFinalDocumentCreateFinalDocPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<StringResponseBase>;
    })
  );
}

apiFinalDocumentCreateFinalDocPost$Plain.PATH = '/api/FinalDocument/CreateFinalDoc';
