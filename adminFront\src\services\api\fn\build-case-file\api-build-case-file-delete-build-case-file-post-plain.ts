/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetBuildCaseFileById } from '../../models/get-build-case-file-by-id';
import { StringResponseBase } from '../../models/string-response-base';

export interface ApiBuildCaseFileDeleteBuildCaseFilePost$Plain$Params {
      body?: GetBuildCaseFileById
}

export function apiBuildCaseFileDeleteBuildCaseFilePost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseFileDeleteBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileDeleteBuildCaseFilePost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<StringResponseBase>;
    })
  );
}

apiBuildCaseFileDeleteBuildCaseFilePost$Plain.PATH = '/api/BuildCaseFile/DeleteBuildCaseFile';
