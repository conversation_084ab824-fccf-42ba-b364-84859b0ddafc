/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetFinalDocAfter } from '../../models/get-final-doc-after';
import { GetFinalDocResListResponseBase } from '../../models/get-final-doc-res-list-response-base';

export interface ApiFinalDocumentGetFinalDocAfterPost$Json$Params {
      body?: GetFinalDocAfter
}

export function apiFinalDocumentGetFinalDocAfterPost$Json(http: HttpClient, rootUrl: string, params?: ApiFinalDocumentGetFinalDocAfterPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetFinalDocResListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiFinalDocumentGetFinalDocAfterPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetFinalDocResListResponseBase>;
    })
  );
}

apiFinalDocumentGetFinalDocAfterPost$Json.PATH = '/api/FinalDocument/GetFinalDocAfter';
