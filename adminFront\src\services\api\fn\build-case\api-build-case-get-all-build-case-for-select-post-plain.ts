/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BuildCaseGetListReponseListResponseBase } from '../../models/build-case-get-list-reponse-list-response-base';
import { GetAllBuildCaseArgs } from '../../models/get-all-build-case-args';

export interface ApiBuildCaseGetAllBuildCaseForSelectPost$Plain$Params {
      body?: GetAllBuildCaseArgs
}

export function apiBuildCaseGetAllBuildCaseForSelectPost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetAllBuildCaseForSelectPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetAllBuildCaseForSelectPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BuildCaseGetListReponseListResponseBase>;
    })
  );
}

apiBuildCaseGetAllBuildCaseForSelectPost$Plain.PATH = '/api/BuildCase/GetAllBuildCaseForSelect';
