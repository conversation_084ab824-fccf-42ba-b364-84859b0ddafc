# Template Viewer 組件使用說明

## 更新內容

已將 `ModuleType` 改為 `CTemplateType`，其中：
- `1` = 客變需求
- 此值從父元件傳入且不可異動

## 使用方式

### 在父元件中使用

```typescript
// 父元件 TypeScript
export class ParentComponent {
  templateData = [
    { CRequirementID: 1, CRequirement: '廚房改裝', CGroupName: '室內裝修' },
    { CRequirementID: 2, CRequirement: '浴室翻新', CGroupName: '室內裝修' },
    // ... 更多資料
  ];

  onTemplateSelected(template: any) {
    console.log('選中的模板:', template);
  }

  onTemplateViewerClose() {
    // 關閉模板查看器的邏輯
  }
}
```

```html
<!-- 父元件 HTML -->
<app-template-viewer
  [availableData]="templateData"
  [templateType]="1"
  [showOnlyAddForm]="false"
  (selectTemplate)="onTemplateSelected($event)"
  (close)="onTemplateViewerClose()">
</app-template-viewer>
```

## 參數說明

| 參數 | 類型 | 說明 | 預設值 |
|------|------|------|--------|
| `availableData` | `any[]` | 父元件傳入的可選資料 | `[]` |
| `templateType` | `number` | 模板類型 (1=客變需求) | `1` |
| `showOnlyAddForm` | `boolean` | 是否只顯示新增模板表單 | `false` |

## 事件說明

| 事件 | 參數 | 說明 |
|------|------|------|
| `selectTemplate` | `Template` | 當選擇模板時觸發 |
| `close` | `void` | 當關閉組件時觸發 |

## 資料結構

### Template
```typescript
interface Template {
  TemplateID?: number;
  TemplateName: string;
  Description?: string;
}
```

### TemplateDetail
```typescript
interface TemplateDetail {
  TemplateDetailID?: number;
  TemplateID: number;
  RefID: number; // 關聯主檔ID
  CTemplateType: number; // 模板類型，1=客變需求
  FieldName: string;
  FieldValue: string;
}
```

## API 對應

組件已準備好對應以下 API：

1. **GET /api/Template/GetTemplateList** - 獲取模板列表
2. **POST /api/Template/SaveTemplate** - 保存模板
3. **POST /api/Template/DeleteTemplate** - 刪除模板
4. **POST /api/Template/GetTemplateById** - 根據ID獲取模板

## 注意事項

1. `templateType` 參數固定為 `1` (客變需求)，從父元件傳入且不可異動
2. 組件內部已更新所有相關的 API 調用和資料處理邏輯
3. 模板詳情結構已調整為使用 `CTemplateType` 而非 `ModuleType`
