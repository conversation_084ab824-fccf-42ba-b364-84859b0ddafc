/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { StringResponseBase } from '../../models/string-response-base';
import { UpdateSignArgs } from '../../models/update-sign-args';

export interface ApiFinalDocumentUpdateSignPost$Plain$Params {
      body?: UpdateSignArgs
}

export function apiFinalDocumentUpdateSignPost$Plain(http: HttpClient, rootUrl: string, params?: ApiFinalDocumentUpdateSignPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiFinalDocumentUpdateSignPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<StringResponseBase>;
    })
  );
}

apiFinalDocumentUpdateSignPost$Plain.PATH = '/api/FinalDocument/UpdateSign';
