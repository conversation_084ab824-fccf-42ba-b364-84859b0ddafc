/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetFinalDocListByHouse } from '../../models/get-final-doc-list-by-house';
import { TblFinalDocumentListResponseBase } from '../../models/tbl-final-document-list-response-base';

export interface ApiFinalDocumentGetListFinalDocByHousePost$Plain$Params {
      body?: GetFinalDocListByHouse
}

export function apiFinalDocumentGetListFinalDocByHousePost$Plain(http: HttpClient, rootUrl: string, params?: ApiFinalDocumentGetListFinalDocByHousePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TblFinalDocumentListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiFinalDocumentGetListFinalDocByHousePost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<TblFinalDocumentListResponseBase>;
    })
  );
}

apiFinalDocumentGetListFinalDocByHousePost$Plain.PATH = '/api/FinalDocument/GetListFinalDocByHouse';
