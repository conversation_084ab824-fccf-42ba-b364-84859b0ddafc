{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nexport var EnumQuotationItemType;\n(function (EnumQuotationItemType) {\n  EnumQuotationItemType[EnumQuotationItemType[\"$1\"] = 1] = \"$1\";\n  EnumQuotationItemType[EnumQuotationItemType[\"$2\"] = 2] = \"$2\";\n  EnumQuotationItemType[EnumQuotationItemType[\"$3\"] = 3] = \"$3\";\n})(EnumQuotationItemType || (EnumQuotationItemType = {}));", "map": {"version": 3, "names": ["EnumQuotationItemType"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\models\\enum-quotation-item-type.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nexport enum EnumQuotationItemType {\r\n  $1 = 1,\r\n  $2 = 2,\r\n  $3 = 3\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,WAAYA,qBAIX;AAJD,WAAYA,qBAAqB;EAC/BA,qBAAA,CAAAA,qBAAA,kBAAM;EACNA,qBAAA,CAAAA,qBAAA,kBAAM;EACNA,qBAAA,CAAAA,qBAAA,kBAAM;AACR,CAAC,EAJWA,qBAAqB,KAArBA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}