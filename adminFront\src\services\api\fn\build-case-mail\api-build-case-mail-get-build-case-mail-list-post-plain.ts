/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetBuildCaseMailListRequest } from '../../models/get-build-case-mail-list-request';
import { GetBuildCaseMailListResponseListResponseBase } from '../../models/get-build-case-mail-list-response-list-response-base';

export interface ApiBuildCaseMailGetBuildCaseMailListPost$Plain$Params {
      body?: GetBuildCaseMailListRequest
}

export function apiBuildCaseMailGetBuildCaseMailListPost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseMailGetBuildCaseMailListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetBuildCaseMailListResponseListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseMailGetBuildCaseMailListPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetBuildCaseMailListResponseListResponseBase>;
    })
  );
}

apiBuildCaseMailGetBuildCaseMailListPost$Plain.PATH = '/api/BuildCaseMail/GetBuildCaseMailList';
