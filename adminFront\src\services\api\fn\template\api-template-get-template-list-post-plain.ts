/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { TemplateGetListArgs } from '../../models/template-get-list-args';
import { TemplateGetListResponseListResponseBase } from '../../models/template-get-list-response-list-response-base';

export interface ApiTemplateGetTemplateListPost$Plain$Params {
      body?: TemplateGetListArgs
}

export function apiTemplateGetTemplateListPost$Plain(http: HttpClient, rootUrl: string, params?: ApiTemplateGetTemplateListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiTemplateGetTemplateListPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<TemplateGetListResponseListResponseBase>;
    })
  );
}

apiTemplateGetTemplateListPost$Plain.PATH = '/api/Template/GetTemplateList';
