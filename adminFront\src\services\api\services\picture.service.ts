/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { apiPictureGetPicturelListPost$Json } from '../fn/picture/api-picture-get-picturel-list-post-json';
import { ApiPictureGetPicturelListPost$Json$Params } from '../fn/picture/api-picture-get-picturel-list-post-json';
import { apiPictureGetPicturelListPost$Plain } from '../fn/picture/api-picture-get-picturel-list-post-plain';
import { ApiPictureGetPicturelListPost$Plain$Params } from '../fn/picture/api-picture-get-picturel-list-post-plain';
import { apiPictureUpdatePicturePost$Json } from '../fn/picture/api-picture-update-picture-post-json';
import { ApiPictureUpdatePicturePost$Json$Params } from '../fn/picture/api-picture-update-picture-post-json';
import { apiPictureUpdatePicturePost$Plain } from '../fn/picture/api-picture-update-picture-post-plain';
import { ApiPictureUpdatePicturePost$Plain$Params } from '../fn/picture/api-picture-update-picture-post-plain';
import { apiPictureUploadListPicturePost$Json } from '../fn/picture/api-picture-upload-list-picture-post-json';
import { ApiPictureUploadListPicturePost$Json$Params } from '../fn/picture/api-picture-upload-list-picture-post-json';
import { apiPictureUploadListPicturePost$Plain } from '../fn/picture/api-picture-upload-list-picture-post-plain';
import { ApiPictureUploadListPicturePost$Plain$Params } from '../fn/picture/api-picture-upload-list-picture-post-plain';
import { GetPictureListResponseListResponseBase } from '../models/get-picture-list-response-list-response-base';
import { UploadFileResponseResponseBase } from '../models/upload-file-response-response-base';

@Injectable({ providedIn: 'root' })
export class PictureService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `apiPictureGetPicturelListPost()` */
  static readonly ApiPictureGetPicturelListPostPath = '/api/Picture/GetPicturelList';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureGetPicturelListPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureGetPicturelListPost$Plain$Response(params?: ApiPictureGetPicturelListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPictureListResponseListResponseBase>> {
    return apiPictureGetPicturelListPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureGetPicturelListPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureGetPicturelListPost$Plain(params?: ApiPictureGetPicturelListPost$Plain$Params, context?: HttpContext): Observable<GetPictureListResponseListResponseBase> {
    return this.apiPictureGetPicturelListPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetPictureListResponseListResponseBase>): GetPictureListResponseListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureGetPicturelListPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureGetPicturelListPost$Json$Response(params?: ApiPictureGetPicturelListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPictureListResponseListResponseBase>> {
    return apiPictureGetPicturelListPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureGetPicturelListPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureGetPicturelListPost$Json(params?: ApiPictureGetPicturelListPost$Json$Params, context?: HttpContext): Observable<GetPictureListResponseListResponseBase> {
    return this.apiPictureGetPicturelListPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetPictureListResponseListResponseBase>): GetPictureListResponseListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiPictureUploadListPicturePost()` */
  static readonly ApiPictureUploadListPicturePostPath = '/api/Picture/UploadListPicture';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureUploadListPicturePost$Plain()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUploadListPicturePost$Plain$Response(params?: ApiPictureUploadListPicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {
    return apiPictureUploadListPicturePost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureUploadListPicturePost$Plain$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUploadListPicturePost$Plain(params?: ApiPictureUploadListPicturePost$Plain$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {
    return this.apiPictureUploadListPicturePost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureUploadListPicturePost$Json()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUploadListPicturePost$Json$Response(params?: ApiPictureUploadListPicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {
    return apiPictureUploadListPicturePost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureUploadListPicturePost$Json$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUploadListPicturePost$Json(params?: ApiPictureUploadListPicturePost$Json$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {
    return this.apiPictureUploadListPicturePost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)
    );
  }

  /** Path part for operation `apiPictureUpdatePicturePost()` */
  static readonly ApiPictureUpdatePicturePostPath = '/api/Picture/UpdatePicture';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureUpdatePicturePost$Plain()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUpdatePicturePost$Plain$Response(params?: ApiPictureUpdatePicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {
    return apiPictureUpdatePicturePost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureUpdatePicturePost$Plain$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUpdatePicturePost$Plain(params?: ApiPictureUpdatePicturePost$Plain$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {
    return this.apiPictureUpdatePicturePost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureUpdatePicturePost$Json()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUpdatePicturePost$Json$Response(params?: ApiPictureUpdatePicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {
    return apiPictureUpdatePicturePost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureUpdatePicturePost$Json$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUpdatePicturePost$Json(params?: ApiPictureUpdatePicturePost$Json$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {
    return this.apiPictureUpdatePicturePost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)
    );
  }

}
