/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { StringResponseBase } from '../../models/string-response-base';

export interface ApiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Params {
      body?: {
'CBuildCaseID'?: number;
'CCategoryName'?: string;
'CFiles'?: Array<Blob>;
}
}

export function apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json(http: HttpClient, rootUrl: string, params?: ApiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'multipart/form-data');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<StringResponseBase>;
    })
  );
}

apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json.PATH = '/api/BuildCaseFile/SaveMultipleBuildCaseFile';
