/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { SpecialChangeAvailableArgs } from '../../models/special-change-available-args';
import { SpecialChangeAvailableResListResponseBase } from '../../models/special-change-available-res-list-response-base';

export interface ApiFinalDocumentGetListSpecialChangeAvailablePost$Json$Params {
      body?: SpecialChangeAvailableArgs
}

export function apiFinalDocumentGetListSpecialChangeAvailablePost$Json(http: HttpClient, rootUrl: string, params?: ApiFinalDocumentGetListSpecialChangeAvailablePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<SpecialChangeAvailableResListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiFinalDocumentGetListSpecialChangeAvailablePost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<SpecialChangeAvailableResListResponseBase>;
    })
  );
}

apiFinalDocumentGetListSpecialChangeAvailablePost$Json.PATH = '/api/FinalDocument/GetListSpecialChangeAvailable';
