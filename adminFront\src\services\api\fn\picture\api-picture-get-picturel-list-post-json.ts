/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetPictureListRequest } from '../../models/get-picture-list-request';
import { GetPictureListResponseListResponseBase } from '../../models/get-picture-list-response-list-response-base';

export interface ApiPictureGetPicturelListPost$Json$Params {
      body?: GetPictureListRequest
}

export function apiPictureGetPicturelListPost$Json(http: HttpClient, rootUrl: string, params?: ApiPictureGetPicturelListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPictureListResponseListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiPictureGetPicturelListPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetPictureListResponseListResponseBase>;
    })
  );
}

apiPictureGetPicturelListPost$Json.PATH = '/api/Picture/GetPicturelList';
