/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetTemplateByIdArgs } from '../../models/get-template-by-id-args';
import { TemplateGetListResponseResponseBase } from '../../models/template-get-list-response-response-base';

export interface ApiTemplateGetTemplateByIdPost$Json$Params {
      body?: GetTemplateByIdArgs
}

export function apiTemplateGetTemplateByIdPost$Json(http: HttpClient, rootUrl: string, params?: ApiTemplateGetTemplateByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiTemplateGetTemplateByIdPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<TemplateGetListResponseResponseBase>;
    })
  );
}

apiTemplateGetTemplateByIdPost$Json.PATH = '/api/Template/GetTemplateById';
