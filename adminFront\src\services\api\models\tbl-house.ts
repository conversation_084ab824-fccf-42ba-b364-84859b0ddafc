/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

export interface TblHouse {
  CBuildCaseId?: number;
  CBuildingName?: string | null;
  CChangeEndDate?: string | null;
  CChangeStartDate?: string | null;
  CCreateDt?: string | null;
  CCreator?: string | null;
  CCustomerName?: string | null;
  CDisclaimerSign?: string | null;
  CFinalDocumentId?: number | null;
  CFloor?: number;
  CHouseType?: number;
  CHousehold?: string | null;
  CHouseholdCode?: string | null;
  CHouseholdDetailId?: number;
  CId?: number;
  CIsChange?: boolean;
  CIsEnable?: boolean;
  CIsLock?: boolean;
  CMail?: string | null;
  CMileStone?: number | null;
  CNationalId?: string | null;
  COnlineQuotationSign?: boolean | null;
  CPayStatus?: number;
  CPhone?: string | null;
  CProgress?: number;
  CQuotationVersionId?: number | null;
  CRegularChangeId?: number;
  CRegularNoticeFileId?: number | null;
  CRegularPictureId?: number | null;
  CSecureKey?: string | null;
  CSignStatus?: number;
  CSpecialChangeId?: number | null;
  CSpecialNoticeFileId?: number | null;
  CTotalPrice?: number | null;
  CUpdateDt?: string | null;
  CUpdator?: string | null;
}
