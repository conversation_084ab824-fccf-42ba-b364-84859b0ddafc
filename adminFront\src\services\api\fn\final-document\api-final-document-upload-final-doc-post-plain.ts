/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { StringResponseBase } from '../../models/string-response-base';

export interface ApiFinalDocumentUploadFinalDocPost$Plain$Params {
      body?: {
'CHouseID'?: number;
'CBuildCaseID'?: number;
'CDocumentName'?: string;
'CNote'?: string;
'CApproveRemark'?: string;
'CFile'?: Blob;
}
}

export function apiFinalDocumentUploadFinalDocPost$Plain(http: HttpClient, rootUrl: string, params?: ApiFinalDocumentUploadFinalDocPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiFinalDocumentUploadFinalDocPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'multipart/form-data');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<StringResponseBase>;
    })
  );
}

apiFinalDocumentUploadFinalDocPost$Plain.PATH = '/api/FinalDocument/UploadFinalDoc';
