/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { DeleteQuotationRequest } from '../../models/delete-quotation-request';
import { StringResponseBase } from '../../models/string-response-base';

export interface ApiQuotationDeleteDataPost$Json$Params {
    body?: DeleteQuotationRequest
}

export function apiQuotationDeleteDataPost$Json(http: HttpClient, rootUrl: string, params?: ApiQuotationDeleteDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    const rb = new RequestBuilder(rootUrl, apiQuotationDeleteDataPost$Json.PATH, 'post');
    if (params) {
        rb.body(params.body, 'application/*+json');
    }

    return http.request(
        rb.build({ responseType: 'json', accept: 'text/json', context })
    ).pipe(
        filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
        map((r: HttpResponse<any>) => {
            return r as StrictHttpResponse<StringResponseBase>;
        })
    );
}

apiQuotationDeleteDataPost$Json.PATH = '/api/Quotation/DeleteData';
