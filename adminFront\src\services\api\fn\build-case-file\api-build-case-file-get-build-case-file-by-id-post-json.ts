/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BuildCaseFileResResponseBase } from '../../models/build-case-file-res-response-base';
import { GetBuildCaseFileById } from '../../models/get-build-case-file-by-id';

export interface ApiBuildCaseFileGetBuildCaseFileByIdPost$Json$Params {
      body?: GetBuildCaseFileById
}

export function apiBuildCaseFileGetBuildCaseFileByIdPost$Json(http: HttpClient, rootUrl: string, params?: ApiBuildCaseFileGetBuildCaseFileByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseFileResResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileGetBuildCaseFileByIdPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BuildCaseFileResResponseBase>;
    })
  );
}

apiBuildCaseFileGetBuildCaseFileByIdPost$Json.PATH = '/api/BuildCaseFile/GetBuildCaseFileByID';
