/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { StringResponseBase } from '../../models/string-response-base';

export interface ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params {
      body?: {
'CBuildCaseID'?: number;
'CBuildCaseFileID'?: number;
'CStatus'?: number;
'CFile'?: Blob;
'CCategoryName'?: string;
'CSubmitRemark'?: string;
}
}

export function apiBuildCaseFileSaveBuildCaseFilePost$Json(http: HttpClient, rootUrl: string, params?: ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileSaveBuildCaseFilePost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'multipart/form-data');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<StringResponseBase>;
    })
  );
}

apiBuildCaseFileSaveBuildCaseFilePost$Json.PATH = '/api/BuildCaseFile/SaveBuildCaseFile';
