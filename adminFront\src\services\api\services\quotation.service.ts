/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { apiQuotationGetListPost$Json } from '../fn/quotation/api-quotation-get-list-post-json';
import { ApiQuotationGetListPost$Json$Params } from '../fn/quotation/api-quotation-get-list-post-json';
import { apiQuotationGetDataPost$Json } from '../fn/quotation/api-quotation-get-data-post-json';
import { ApiQuotationGetDataPost$Json$Params } from '../fn/quotation/api-quotation-get-data-post-json';
import { apiQuotationSaveDataPost$Json } from '../fn/quotation/api-quotation-save-data-post-json';
import { ApiQuotationSaveDataPost$Json$Params } from '../fn/quotation/api-quotation-save-data-post-json';
import { apiQuotationDeleteDataPost$Json } from '../fn/quotation/api-quotation-delete-data-post-json';
import { ApiQuotationDeleteDataPost$Json$Params } from '../fn/quotation/api-quotation-delete-data-post-json';
import { apiQuotationGetListByHouseIdPost$Json } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';
import { ApiQuotationGetListByHouseIdPost$Json$Params } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';
import { GetQuotationListResponseBase } from '../models/get-quotation-list-response-base';
import { GetQuotationResponseBase } from '../models/get-quotation-response-base';
import { StringResponseBase } from '../models/string-response-base';

@Injectable({ providedIn: 'root' })
export class QuotationService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `apiQuotationGetListPost()` */
  static readonly ApiQuotationGetListPostPath = '/api/Quotation/GetList';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetListPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListPost$Json$Response(params?: ApiQuotationGetListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {
    return apiQuotationGetListPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetListPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListPost$Json(params?: ApiQuotationGetListPost$Json$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {
    return this.apiQuotationGetListPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationGetDataPost()` */
  static readonly ApiQuotationGetDataPostPath = '/api/Quotation/GetData';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetDataPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetDataPost$Json$Response(params?: ApiQuotationGetDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationResponseBase>> {
    return apiQuotationGetDataPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetDataPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetDataPost$Json(params?: ApiQuotationGetDataPost$Json$Params, context?: HttpContext): Observable<GetQuotationResponseBase> {
    return this.apiQuotationGetDataPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationResponseBase>): GetQuotationResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationSaveDataPost()` */
  static readonly ApiQuotationSaveDataPostPath = '/api/Quotation/SaveData';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationSaveDataPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationSaveDataPost$Json$Response(params?: ApiQuotationSaveDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiQuotationSaveDataPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */  apiQuotationSaveDataPost$Json(params?: ApiQuotationSaveDataPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiQuotationSaveDataPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationDeleteDataPost()` */
  static readonly ApiQuotationDeleteDataPostPath = '/api/Quotation/DeleteData';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationDeleteDataPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationDeleteDataPost$Json$Response(params?: ApiQuotationDeleteDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiQuotationDeleteDataPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationDeleteDataPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationDeleteDataPost$Json(params?: ApiQuotationDeleteDataPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiQuotationDeleteDataPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationGetListByHouseIdPost()` */
  static readonly ApiQuotationGetListByHouseIdPostPath = '/api/Quotation/GetListByHouseID';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetListByHouseIdPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListByHouseIdPost$Json$Response(params?: ApiQuotationGetListByHouseIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {
    return apiQuotationGetListByHouseIdPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListByHouseIdPost$Json(params?: ApiQuotationGetListByHouseIdPost$Json$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {
    return this.apiQuotationGetListByHouseIdPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)
    );
  }
}
