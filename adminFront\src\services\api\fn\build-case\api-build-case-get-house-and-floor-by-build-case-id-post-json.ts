/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetHouseAndFloorByBuildCaseIdResListResponseBase } from '../../models/get-house-and-floor-by-build-case-id-res-list-response-base';

export interface ApiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Params {
  buildCaseId?: number;
}

export function apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseAndFloorByBuildCaseIdResListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json.PATH, 'post');
  if (params) {
    rb.query('buildCaseId', params.buildCaseId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetHouseAndFloorByBuildCaseIdResListResponseBase>;
    })
  );
}

apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json.PATH = '/api/BuildCase/GetHouseAndFloorByBuildCaseId';
