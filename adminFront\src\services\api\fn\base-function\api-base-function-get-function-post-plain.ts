/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { EnumArgs } from '../../models/enum-args';
import { EnumResponseListResponseBase } from '../../models/enum-response-list-response-base';

export interface ApiBaseFunctionGetFunctionPost$Plain$Params {
      body?: EnumArgs
}

export function apiBaseFunctionGetFunctionPost$Plain(http: HttpClient, rootUrl: string, params?: ApiBaseFunctionGetFunctionPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<EnumResponseListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBaseFunctionGetFunctionPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<EnumResponseListResponseBase>;
    })
  );
}

apiBaseFunctionGetFunctionPost$Plain.PATH = '/api/BaseFunction/GetFunction';
