import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NbCardModule, NbButtonModule } from '@nebular/theme';

@Component({
  selector: 'app-template-viewer',
  templateUrl: './template-viewer.component.html',
  styleUrls: ['./template-viewer.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],
})
export class TemplateViewerComponent implements OnInit, OnChanges {
  @Input() availableData: any[] = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)
  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動
  @Input() showOnlyAddForm: boolean = false; // 是否只顯示新增模板表單
  @Output() selectTemplate = new EventEmitter<Template>();
  @Output() close = new EventEmitter<void>(); // 關閉事件

  // 公開Math對象供模板使用
  Math = Math;

  // 內部管理的模板資料
  templates: Template[] = [];
  templateDetails: TemplateDetail[] = [];
  selectedTemplate: Template | null = null;

  // 查詢功能
  searchKeyword = '';
  filteredTemplates: Template[] = [];

  // 分頁功能 - 模板列表
  templatePagination = {
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0
  };
  paginatedTemplates: Template[] = [];

  // 分頁功能 - 模板詳情
  detailPagination = {
    currentPage: 1,
    pageSize: 5,
    totalItems: 0,
    totalPages: 0
  };
  paginatedDetails: TemplateDetail[] = [];

  // 新增模板表單
  showAddForm = false;
  newTemplate = {
    name: '',
    description: '',
    selectedItems: [] as any[]
  };

  ngOnInit() {
    this.loadTemplates();
    this.updateFilteredTemplates();

    // 如果只顯示新增表單，自動打開新增模板表單
    if (this.showOnlyAddForm) {
      this.onAddTemplate();
    }
  }

  ngOnChanges() {
    this.updateFilteredTemplates();
  }

  // TODO: 替換為實際的API調用
  loadTemplates() {
    // 模擬API調用 - 載入模板列表
    console.log('載入模板 API 調用:', { templateType: this.templateType });

    // 模擬更多數據來測試分頁功能
    this.templates = [];
    for (let i = 1; i <= 25; i++) {
      this.templates.push({
        TemplateID: i,
        TemplateName: `需求模板${String.fromCharCode(64 + i)}`,
        Description: `包含${i % 2 === 0 ? '進階' : '基本'}工程項目的模板`
      });
    }

    // 載入對應模板類型的模板詳情 - 模擬更多數據
    this.templateDetails = [];
    let detailId = 1;

    for (let templateId = 1; templateId <= 25; templateId++) {
      // 每個模板隨機生成3-8個詳情項目
      const itemCount = Math.floor(Math.random() * 6) + 3;

      for (let j = 1; j <= itemCount; j++) {
        this.templateDetails.push({
          TemplateDetailID: detailId++,
          TemplateID: templateId,
          RefID: templateId * 100 + j,
          CTemplateType: this.templateType,
          FieldName: this.getFieldName({}),
          FieldValue: `工程項目${String.fromCharCode(64 + templateId)}-${j}`
        });
      }
    }
  }

  // 更新過濾後的模板列表
  updateFilteredTemplates() {
    if (!this.searchKeyword.trim()) {
      this.filteredTemplates = [...this.templates];
    } else {
      const keyword = this.searchKeyword.toLowerCase();
      this.filteredTemplates = this.templates.filter(template =>
        template.TemplateName.toLowerCase().includes(keyword) ||
        (template.Description && template.Description.toLowerCase().includes(keyword))
      );
    }
    this.updateTemplatePagination();
  }

  // 更新模板分頁
  updateTemplatePagination() {
    this.templatePagination.totalItems = this.filteredTemplates.length;
    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);

    // 確保當前頁面不超過總頁數
    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {
      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);
    }

    this.updatePaginatedTemplates();
  }

  // 更新分頁後的模板列表
  updatePaginatedTemplates() {
    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;
    const endIndex = startIndex + this.templatePagination.pageSize;
    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);
  }

  // 模板分頁導航
  goToTemplatePage(page: number) {
    if (page >= 1 && page <= this.templatePagination.totalPages) {
      this.templatePagination.currentPage = page;
      this.updatePaginatedTemplates();
    }
  }

  // 獲取模板分頁頁碼數組
  getTemplatePageNumbers(): number[] {
    const pages: number[] = [];
    const totalPages = this.templatePagination.totalPages;
    const currentPage = this.templatePagination.currentPage;

    // 顯示當前頁面前後各2頁
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  // 搜尋模板
  onSearch() {
    this.updateFilteredTemplates();
  }

  // 清除搜尋
  clearSearch() {
    this.searchKeyword = '';
    this.updateFilteredTemplates();
  }



  // 顯示新增模板表單
  onAddTemplate() {
    this.showAddForm = true;
    this.newTemplate = {
      name: '',
      description: '',
      selectedItems: []
    };
    // 如果不是只顯示新增表單模式，才重置選擇狀態
    // 在只顯示新增表單模式下，保持父元件傳入的選中狀態
    if (!this.showOnlyAddForm) {
      this.availableData.forEach(item => item.selected = false);
    }
  }

  // 取消新增模板
  cancelAddTemplate() {
    this.showAddForm = false;
    this.newTemplate = {
      name: '',
      description: '',
      selectedItems: []
    };
  }

  // 儲存新模板
  saveNewTemplate() {
    if (!this.newTemplate.name.trim()) {
      alert('請輸入模板名稱');
      return;
    }

    const selectedItems = this.availableData.filter(item => item.selected);
    if (selectedItems.length === 0) {
      alert('請至少選擇一個項目');
      return;
    }

    // TODO: 替換為實際的API調用
    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);
  }

  // TODO: 替換為實際的API調用
  createTemplate(name: string, description: string, selectedItems: any[]) {
    // 模擬API調用 - 創建模板
    console.log('創建模板 API 調用:', {
      templateName: name,
      description: description,
      templateType: this.templateType,
      selectedItems: selectedItems.map(item => ({
        refId: this.getRefId(item),
        fieldName: this.getFieldName(item),
        fieldValue: this.getFieldValue(item)
      }))
    });

    // 生成新的模板ID (實際應由後端API返回)
    const newId = Math.max(...this.templates.map(t => t.TemplateID || 0), 0) + 1;

    // 創建新模板
    const newTemplate: Template = {
      TemplateID: newId,
      TemplateName: name.trim(),
      Description: description.trim()
    };

    // 添加到模板列表
    this.templates.push(newTemplate);

    // 創建模板詳情 - 根據選中的項目創建詳情記錄
    selectedItems.forEach((item, index) => {
      const detail: TemplateDetail = {
        TemplateDetailID: this.templateDetails.length + index + 1,
        TemplateID: newId,
        RefID: this.getRefId(item), // 關聯主檔ID
        CTemplateType: this.templateType, // 模板類型，1=客變需求，從父元件傳入
        FieldName: this.getFieldName(item), // 欄位名稱
        FieldValue: this.getFieldValue(item) // 欄位值
      };
      this.templateDetails.push(detail);
    });

    // 更新過濾列表
    this.updateFilteredTemplates();

    // 關閉表單
    this.showAddForm = false;
    alert(`模板 "${name}" 已成功創建！包含 ${selectedItems.length} 個項目`);
  }

  // 獲取關聯主檔ID的輔助方法
  private getRefId(item: any): number {
    return item.CRequirementID || item.ID || item.id || 0;
  }

  // 獲取欄位名稱的輔助方法
  private getFieldName(_item?: any): string {
    // 根據模板類型決定欄位名稱
    switch (this.templateType) {
      case 1: // 客變需求
        return 'CRequirement';
      default:
        return 'name';
    }
  }

  // 獲取欄位值的輔助方法
  private getFieldValue(item: any): string {
    return item.CRequirement || item.name || item.title || '';
  }

  // 查看模板
  onSelectTemplate(template: Template) {
    this.selectedTemplate = template;
    this.selectTemplate.emit(template);
    this.updateDetailPagination();
  }

  // 更新詳情分頁
  updateDetailPagination() {
    const details = this.currentTemplateDetails;
    this.detailPagination.totalItems = details.length;
    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);
    this.detailPagination.currentPage = 1; // 重置到第一頁
    this.updatePaginatedDetails();
  }

  // 更新分頁後的詳情列表
  updatePaginatedDetails() {
    const details = this.currentTemplateDetails;
    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;
    const endIndex = startIndex + this.detailPagination.pageSize;
    this.paginatedDetails = details.slice(startIndex, endIndex);
  }

  // 詳情分頁導航
  goToDetailPage(page: number) {
    if (page >= 1 && page <= this.detailPagination.totalPages) {
      this.detailPagination.currentPage = page;
      this.updatePaginatedDetails();
    }
  }

  // 獲取詳情分頁頁碼數組
  getDetailPageNumbers(): number[] {
    const pages: number[] = [];
    const totalPages = this.detailPagination.totalPages;
    const currentPage = this.detailPagination.currentPage;

    // 顯示當前頁面前後各2頁
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  // 關閉模板查看器
  onClose() {
    this.close.emit();
  }

  // 刪除模板
  onDeleteTemplate(templateID: number) {
    if (confirm('確定刪除此模板？')) {
      // TODO: 替換為實際的API調用
      this.deleteTemplateById(templateID);
    }
  }

  // TODO: 替換為實際的API調用
  deleteTemplateById(templateID: number) {
    // 模擬API調用 - 刪除模板
    console.log('刪除模板 API 調用:', {
      templateID: templateID,
      templateType: this.templateType
    });

    // 刪除模板和相關詳情
    this.templates = this.templates.filter(t => t.TemplateID !== templateID);
    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);
    this.updateFilteredTemplates();

    // 如果當前查看的模板被刪除，關閉詳情
    if (this.selectedTemplate?.TemplateID === templateID) {
      this.selectedTemplate = null;
    }

    alert('模板已刪除');
  }

  /*
   * API 設計說明：
   *
   * 1. 載入模板列表 API:
   *    GET /api/Template/GetTemplateList
   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }
   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }
   *
   * 2. 創建模板 API:
   *    POST /api/Template/SaveTemplate
   *    請求體: {
   *      CTemplateName: string,
   *      CTemplateType: number,  // 1=客變需求
   *      CStatus: number,
   *      Details: [{
   *        CReleateId: number,        // 關聯主檔ID
   *        CReleateName: string       // 關聯名稱
   *      }]
   *    }
   *
   * 3. 刪除模板 API:
   *    POST /api/Template/DeleteTemplate
   *    請求體: { CTemplateId: number }
   *
   * 資料庫設計重點：
   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)
   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)
   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)
   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱
   */

  // 關閉模板詳情
  closeTemplateDetail() {
    this.selectedTemplate = null;
  }

  // 取得當前選中模板的詳情
  get currentTemplateDetails(): TemplateDetail[] {
    if (!this.selectedTemplate) {
      return [];
    }
    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);
  }

  // TrackBy函數用於優化ngFor性能
  trackByTemplateId(index: number, template: Template): number {
    return template.TemplateID || index;
  }
}

// DB 對應型別
export interface Template {
  TemplateID?: number;
  TemplateName: string;
  Description?: string;
}
export interface TemplateDetail {
  TemplateDetailID?: number;
  TemplateID: number;
  RefID: number; // 關聯主檔ID
  CTemplateType: number; // 模板類型，1=客變需求
  FieldName: string;
  FieldValue: string;
}
