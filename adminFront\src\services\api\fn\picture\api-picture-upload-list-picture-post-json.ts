/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { UploadFileResponseResponseBase } from '../../models/upload-file-response-response-base';

export interface ApiPictureUploadListPicturePost$Json$Params {
      body?: {
'CBuildCaseId'?: number;
'CPath'?: string;
'CFile'?: Array<Blob>;
}
}

export function apiPictureUploadListPicturePost$Json(http: HttpClient, rootUrl: string, params?: ApiPictureUploadListPicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiPictureUploadListPicturePost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'multipart/form-data');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<UploadFileResponseResponseBase>;
    })
  );
}

apiPictureUploadListPicturePost$Json.PATH = '/api/Picture/UploadListPicture';
