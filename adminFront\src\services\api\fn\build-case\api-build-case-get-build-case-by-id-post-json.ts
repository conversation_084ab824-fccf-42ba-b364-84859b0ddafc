/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BuildCaseGetListReponseResponseBase } from '../../models/build-case-get-list-reponse-response-base';
import { GetBuildCaseById } from '../../models/get-build-case-by-id';

export interface ApiBuildCaseGetBuildCaseByIdPost$Json$Params {
      body?: GetBuildCaseById
}

export function apiBuildCaseGetBuildCaseByIdPost$Json(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetBuildCaseByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetBuildCaseByIdPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BuildCaseGetListReponseResponseBase>;
    })
  );
}

apiBuildCaseGetBuildCaseByIdPost$Json.PATH = '/api/BuildCase/GetBuildCaseByID';
