/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetFinalDocListByHouse } from '../../models/get-final-doc-list-by-house';
import { TblFinalDocumentListResponseBase } from '../../models/tbl-final-document-list-response-base';

export interface ApiFinalDocumentGetListFinalDocByHousePost$Json$Params {
      body?: GetFinalDocListByHouse
}

export function apiFinalDocumentGetListFinalDocByHousePost$Json(http: HttpClient, rootUrl: string, params?: ApiFinalDocumentGetListFinalDocByHousePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TblFinalDocumentListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiFinalDocumentGetListFinalDocByHousePost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<TblFinalDocumentListResponseBase>;
    })
  );
}

apiFinalDocumentGetListFinalDocByHousePost$Json.PATH = '/api/FinalDocument/GetListFinalDocByHouse';
