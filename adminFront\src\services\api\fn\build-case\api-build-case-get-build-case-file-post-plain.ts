/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BuildCaseGetFileArgs } from '../../models/build-case-get-file-args';
import { BuildCaseGetFileResponeListResponseBase } from '../../models/build-case-get-file-respone-list-response-base';

export interface ApiBuildCaseGetBuildCaseFilePost$Plain$Params {
      body?: BuildCaseGetFileArgs
}

export function apiBuildCaseGetBuildCaseFilePost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetFileResponeListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetBuildCaseFilePost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BuildCaseGetFileResponeListResponseBase>;
    })
  );
}

apiBuildCaseGetBuildCaseFilePost$Plain.PATH = '/api/BuildCase/GetBuildCaseFile';
