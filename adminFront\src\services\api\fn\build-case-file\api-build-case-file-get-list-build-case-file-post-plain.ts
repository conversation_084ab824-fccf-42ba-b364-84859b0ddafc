/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BuildCaseFileResListResponseBase } from '../../models/build-case-file-res-list-response-base';
import { GetListBuildCaseFileArgs } from '../../models/get-list-build-case-file-args';

export interface ApiBuildCaseFileGetListBuildCaseFilePost$Plain$Params {
      body?: GetListBuildCaseFileArgs
}

export function apiBuildCaseFileGetListBuildCaseFilePost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseFileGetListBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseFileResListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileGetListBuildCaseFilePost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BuildCaseFileResListResponseBase>;
    })
  );
}

apiBuildCaseFileGetListBuildCaseFilePost$Plain.PATH = '/api/BuildCaseFile/GetListBuildCaseFile';
