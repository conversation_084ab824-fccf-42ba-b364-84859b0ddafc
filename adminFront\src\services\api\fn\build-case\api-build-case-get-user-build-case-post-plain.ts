/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BuildCaseGetListReponseListResponseBase } from '../../models/build-case-get-list-reponse-list-response-base';
import { GetUserBuildCaseArgs } from '../../models/get-user-build-case-args';

export interface ApiBuildCaseGetUserBuildCasePost$Plain$Params {
      body?: GetUserBuildCaseArgs
}

export function apiBuildCaseGetUserBuildCasePost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetUserBuildCasePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetUserBuildCasePost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BuildCaseGetListReponseListResponseBase>;
    })
  );
}

apiBuildCaseGetUserBuildCasePost$Plain.PATH = '/api/BuildCase/GetUserBuildCase';
