/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BuildCaseFileResListResponseBase } from '../../models/build-case-file-res-list-response-base';
import { GetListBuildCaseFileArgs } from '../../models/get-list-build-case-file-args';

export interface ApiBuildCaseFileGetListBuildCaseFilePost$Json$Params {
      body?: GetListBuildCaseFileArgs
}

export function apiBuildCaseFileGetListBuildCaseFilePost$Json(http: HttpClient, rootUrl: string, params?: ApiBuildCaseFileGetListBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseFileResListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileGetListBuildCaseFilePost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BuildCaseFileResListResponseBase>;
    })
  );
}

apiBuildCaseFileGetListBuildCaseFilePost$Json.PATH = '/api/BuildCaseFile/GetListBuildCaseFile';
