/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetListFinalDocArgs } from '../../models/get-list-final-doc-args';
import { GetListFinalDocResListResponseBase } from '../../models/get-list-final-doc-res-list-response-base';

export interface ApiFinalDocumentGetListFinalDocPost$Json$Params {
      body?: GetListFinalDocArgs
}

export function apiFinalDocumentGetListFinalDocPost$Json(http: HttpClient, rootUrl: string, params?: ApiFinalDocumentGetListFinalDocPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListFinalDocResListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiFinalDocumentGetListFinalDocPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetListFinalDocResListResponseBase>;
    })
  );
}

apiFinalDocumentGetListFinalDocPost$Json.PATH = '/api/FinalDocument/GetListFinalDoc';
