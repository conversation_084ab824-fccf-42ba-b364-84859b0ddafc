/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BuildCaseGetFileArgs } from '../../models/build-case-get-file-args';
import { BuildCaseGetFileResponeListResponseBase } from '../../models/build-case-get-file-respone-list-response-base';

export interface ApiBuildCaseGetBuildCaseFilePost$Json$Params {
      body?: BuildCaseGetFileArgs
}

export function apiBuildCaseGetBuildCaseFilePost$Json(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetFileResponeListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetBuildCaseFilePost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BuildCaseGetFileResponeListResponseBase>;
    })
  );
}

apiBuildCaseGetBuildCaseFilePost$Json.PATH = '/api/BuildCase/GetBuildCaseFile';
