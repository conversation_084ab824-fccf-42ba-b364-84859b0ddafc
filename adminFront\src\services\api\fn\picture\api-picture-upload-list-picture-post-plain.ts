/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { UploadFileResponseResponseBase } from '../../models/upload-file-response-response-base';

export interface ApiPictureUploadListPicturePost$Plain$Params {
      body?: {
'CBuildCaseId'?: number;
'CPath'?: string;
'CFile'?: Array<Blob>;
}
}

export function apiPictureUploadListPicturePost$Plain(http: HttpClient, rootUrl: string, params?: ApiPictureUploadListPicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiPictureUploadListPicturePost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'multipart/form-data');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<UploadFileResponseResponseBase>;
    })
  );
}

apiPictureUploadListPicturePost$Plain.PATH = '/api/Picture/UploadListPicture';
