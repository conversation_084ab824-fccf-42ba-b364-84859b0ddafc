/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BuildCaseGetListReponseListResponseBase } from '../../models/build-case-get-list-reponse-list-response-base';
import { GetUserBuildCaseArgs } from '../../models/get-user-build-case-args';

export interface ApiBuildCaseGetUserBuildCasePost$Json$Params {
      body?: GetUserBuildCaseArgs
}

export function apiBuildCaseGetUserBuildCasePost$Json(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetUserBuildCasePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetUserBuildCasePost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BuildCaseGetListReponseListResponseBase>;
    })
  );
}

apiBuildCaseGetUserBuildCasePost$Json.PATH = '/api/BuildCase/GetUserBuildCase';
