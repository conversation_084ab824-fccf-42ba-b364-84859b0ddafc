/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BuildCaseGetListReponseListResponseBase } from '../../models/build-case-get-list-reponse-list-response-base';

export interface ApiBuildCaseGetBuildCaseListPost$Plain$Params {
}

export function apiBuildCaseGetBuildCaseListPost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetBuildCaseListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetBuildCaseListPost$Plain.PATH, 'post');
  if (params) {
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BuildCaseGetListReponseListResponseBase>;
    })
  );
}

apiBuildCaseGetBuildCaseListPost$Plain.PATH = '/api/BuildCase/GetBuildCaseList';
