/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetQuotationByIdRequest } from '../../models/get-quotation-by-id-request';
import { GetQuotationResponseBase } from '../../models/get-quotation-response-base';

export interface ApiQuotationGetDataPost$Json$Params {
  body?: GetQuotationByIdRequest
}

export function apiQuotationGetDataPost$Json(http: HttpClient, rootUrl: string, params?: ApiQuotationGetDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiQuotationGetDataPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetQuotationResponseBase>;
    })
  );
}

apiQuotationGetDataPost$Json.PATH = '/api/Quotation/GetData';
