{"ast": null, "code": "import { ApiConfiguration } from './api-configuration';\nimport { BaseFunctionService } from './services/base-function.service';\nimport { BuildCaseService } from './services/build-case.service';\nimport { BuildCaseFileService } from './services/build-case-file.service';\nimport { BuildCaseMailService } from './services/build-case-mail.service';\nimport { FinalDocumentService } from './services/final-document.service';\nimport { FormItemService } from './services/form-item.service';\nimport { HouseService } from './services/house.service';\nimport { HouseHoldMainService } from './services/house-hold-main.service';\nimport { InfoPictureService } from './services/info-picture.service';\nimport { MaterialService } from './services/material.service';\nimport { PictureService } from './services/picture.service';\nimport { PreOrderSettingService } from './services/pre-order-setting.service';\nimport { QuotationService } from './services/quotation.service';\nimport { RegularChangeItemService } from './services/regular-change-item.service';\nimport { RegularNoticeFileService } from './services/regular-notice-file.service';\nimport { RequirementService } from './services/requirement.service';\nimport { ReviewService } from './services/review.service';\nimport { SpecialChangeService } from './services/special-change.service';\nimport { SpecialNoticeFileService } from './services/special-notice-file.service';\nimport { TemplateService } from './services/template.service';\nimport { UserService } from './services/user.service';\nimport { UserGroupService } from './services/user-group.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\n/**\n * Module that provides all services and configuration.\n */\nexport class ApiModule {\n  static forRoot(params) {\n    return {\n      ngModule: ApiModule,\n      providers: [{\n        provide: ApiConfiguration,\n        useValue: params\n      }]\n    };\n  }\n  constructor(parentModule, http) {\n    if (parentModule) {\n      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');\n    }\n    if (!http) {\n      throw new Error('You need to import the HttpClientModule in your AppModule! \\n' + 'See also https://github.com/angular/angular/issues/20575');\n    }\n  }\n  static {\n    this.ɵfac = function ApiModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApiModule)(i0.ɵɵinject(ApiModule, 12), i0.ɵɵinject(i1.HttpClient, 8));\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ApiModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [BaseFunctionService, BuildCaseService, BuildCaseFileService, BuildCaseMailService, FinalDocumentService, FormItemService, HouseService, HouseHoldMainService, InfoPictureService, MaterialService, PictureService, PreOrderSettingService, QuotationService, RegularChangeItemService, RegularNoticeFileService, RequirementService, ReviewService, SpecialChangeService, SpecialNoticeFileService, TemplateService, UserService, UserGroupService, QuotationService, ApiConfiguration]\n    });\n  }\n}", "map": {"version": 3, "names": ["ApiConfiguration", "BaseFunctionService", "BuildCaseService", "BuildCaseFileService", "BuildCaseMailService", "FinalDocumentService", "FormItemService", "HouseService", "HouseHoldMainService", "InfoPictureService", "MaterialService", "PictureService", "PreOrderSettingService", "QuotationService", "RegularChangeItemService", "RegularNoticeFileService", "RequirementService", "ReviewService", "SpecialChangeService", "SpecialNoticeFileService", "TemplateService", "UserService", "UserGroupService", "ApiModule", "forRoot", "params", "ngModule", "providers", "provide", "useValue", "constructor", "parentModule", "http", "Error", "i0", "ɵɵinject", "i1", "HttpClient"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\api.module.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { NgModule, ModuleWithProviders, SkipSelf, Optional } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { ApiConfiguration, ApiConfigurationParams } from './api-configuration';\r\n\r\nimport { BaseFunctionService } from './services/base-function.service';\r\nimport { BuildCaseService } from './services/build-case.service';\r\nimport { BuildCaseFileService } from './services/build-case-file.service';\r\nimport { BuildCaseMailService } from './services/build-case-mail.service';\r\nimport { FinalDocumentService } from './services/final-document.service';\r\nimport { FormItemService } from './services/form-item.service';\r\nimport { HouseService } from './services/house.service';\r\nimport { HouseHoldMainService } from './services/house-hold-main.service';\r\nimport { InfoPictureService } from './services/info-picture.service';\r\nimport { MaterialService } from './services/material.service';\r\nimport { PictureService } from './services/picture.service';\r\nimport { PreOrderSettingService } from './services/pre-order-setting.service';\r\nimport { QuotationService } from './services/quotation.service';\r\nimport { RegularChangeItemService } from './services/regular-change-item.service';\r\nimport { RegularNoticeFileService } from './services/regular-notice-file.service';\r\nimport { RequirementService } from './services/requirement.service';\r\nimport { ReviewService } from './services/review.service';\r\nimport { SpecialChangeService } from './services/special-change.service';\r\nimport { SpecialNoticeFileService } from './services/special-notice-file.service';\r\nimport { TemplateService } from './services/template.service';\r\nimport { UserService } from './services/user.service';\r\nimport { UserGroupService } from './services/user-group.service';\r\nimport { QuotationService } from './services/quotation.service';\r\n\r\n/**\r\n * Module that provides all services and configuration.\r\n */\r\n@NgModule({\r\n  imports: [],\r\n  exports: [],\r\n  declarations: [],\r\n  providers: [\r\n    BaseFunctionService,\r\n    BuildCaseService,\r\n    BuildCaseFileService,\r\n    BuildCaseMailService,\r\n    FinalDocumentService,\r\n    FormItemService,\r\n    HouseService,\r\n    HouseHoldMainService,\r\n    InfoPictureService,\r\n    MaterialService,\r\n    PictureService,\r\n    PreOrderSettingService,\r\n    QuotationService,\r\n    RegularChangeItemService,\r\n    RegularNoticeFileService,\r\n    RequirementService,\r\n    ReviewService,\r\n    SpecialChangeService,\r\n    SpecialNoticeFileService,\r\n    TemplateService,\r\n    UserService,\r\n    UserGroupService,\r\n    QuotationService,\r\n    ApiConfiguration\r\n  ],\r\n})\r\nexport class ApiModule {\r\n  static forRoot(params: ApiConfigurationParams): ModuleWithProviders<ApiModule> {\r\n    return {\r\n      ngModule: ApiModule,\r\n      providers: [\r\n        {\r\n          provide: ApiConfiguration,\r\n          useValue: params\r\n        }\r\n      ]\r\n    }\r\n  }\r\n\r\n  constructor( \r\n    @Optional() @SkipSelf() parentModule: ApiModule,\r\n    @Optional() http: HttpClient\r\n  ) {\r\n    if (parentModule) {\r\n      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');\r\n    }\r\n    if (!http) {\r\n      throw new Error('You need to import the HttpClientModule in your AppModule! \\n' +\r\n      'See also https://github.com/angular/angular/issues/20575');\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAMA,SAASA,gBAAgB,QAAgC,qBAAqB;AAE9E,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,oBAAoB,QAAQ,oCAAoC;AACzE,SAASC,oBAAoB,QAAQ,oCAAoC;AACzE,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,oBAAoB,QAAQ,oCAAoC;AACzE,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,sBAAsB,QAAQ,sCAAsC;AAC7E,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,wBAAwB,QAAQ,wCAAwC;AACjF,SAASC,wBAAwB,QAAQ,wCAAwC;AACjF,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,wBAAwB,QAAQ,wCAAwC;AACjF,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,QAAQ,+BAA+B;;;AAGhE;;;AAkCA,OAAM,MAAOC,SAAS;EACpB,OAAOC,OAAOA,CAACC,MAA8B;IAC3C,OAAO;MACLC,QAAQ,EAAEH,SAAS;MACnBI,SAAS,EAAE,CACT;QACEC,OAAO,EAAE5B,gBAAgB;QACzB6B,QAAQ,EAAEJ;OACX;KAEJ;EACH;EAEAK,YAC0BC,YAAuB,EACnCC,IAAgB;IAE5B,IAAID,YAAY,EAAE;MAChB,MAAM,IAAIE,KAAK,CAAC,kEAAkE,CAAC;IACrF;IACA,IAAI,CAACD,IAAI,EAAE;MACT,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAC/E,0DAA0D,CAAC;IAC7D;EACF;;;uCAxBWV,SAAS,EAAAW,EAAA,CAAAC,QAAA,CAAAZ,SAAA,OAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAATd;IAAS;EAAA;;;iBA3BT,CACTtB,mBAAmB,EACnBC,gBAAgB,EAChBC,oBAAoB,EACpBC,oBAAoB,EACpBC,oBAAoB,EACpBC,eAAe,EACfC,YAAY,EACZC,oBAAoB,EACpBC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,sBAAsB,EACtBC,gBAAgB,EAChBC,wBAAwB,EACxBC,wBAAwB,EACxBC,kBAAkB,EAClBC,aAAa,EACbC,oBAAoB,EACpBC,wBAAwB,EACxBC,eAAe,EACfC,WAAW,EACXC,gBAAgB,EAChBT,gBAAgB,EAChBb,gBAAgB;IACjB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}